{{/* QQ表情处理器 - 简单版本 */}}
{{- $content := .content -}}

{{/* 简单的表情替换 */}}
{{- $processedContent := $content -}}
{{- $processedContent = replace $processedContent ":/微笑:" `<img src="https://koishi.js.org/QFace/assets/qq_emoji/resfile/emoji/277/apng/277.png" class="qmoji" alt=":/微笑:" title=":/微笑:" />` -}}
{{- $processedContent = replace $processedContent ":/撇嘴:" `<img src="https://koishi.js.org/QFace/assets/qq_emoji/resfile/emoji/278/apng/278.png" class="qmoji" alt=":/撇嘴:" title=":/撇嘴:" />` -}}
{{- $processedContent = replace $processedContent ":/色:" `<img src="https://koishi.js.org/QFace/assets/qq_emoji/resfile/emoji/279/apng/279.png" class="qmoji" alt=":/色:" title=":/色:" />` -}}
{{- $processedContent = replace $processedContent ":/发呆:" `<img src="https://koishi.js.org/QFace/assets/qq_emoji/resfile/emoji/280/apng/280.png" class="qmoji" alt=":/发呆:" title=":/发呆:" />` -}}
{{- $processedContent = replace $processedContent ":/得意:" `<img src="https://koishi.js.org/QFace/assets/qq_emoji/resfile/emoji/281/apng/281.png" class="qmoji" alt=":/得意:" title=":/得意:" />` -}}
{{- $processedContent = replace $processedContent ":/大哭:" `<img src="https://koishi.js.org/QFace/assets/qq_emoji/resfile/emoji/282/apng/282.png" class="qmoji" alt=":/大哭:" title=":/大哭:" />` -}}
{{- $processedContent = replace $processedContent ":/害羞:" `<img src="https://koishi.js.org/QFace/assets/qq_emoji/resfile/emoji/283/apng/283.png" class="qmoji" alt=":/害羞:" title=":/害羞:" />` -}}
{{- $processedContent = replace $processedContent ":/闭嘴:" `<img src="https://koishi.js.org/QFace/assets/qq_emoji/resfile/emoji/284/apng/284.png" class="qmoji" alt=":/闭嘴:" title=":/闭嘴:" />` -}}
{{- $processedContent = replace $processedContent ":/睡:" `<img src="https://koishi.js.org/QFace/assets/qq_emoji/resfile/emoji/285/apng/285.png" class="qmoji" alt=":/睡:" title=":/睡:" />` -}}
{{- $processedContent = replace $processedContent ":/尴尬:" `<img src="https://koishi.js.org/QFace/assets/qq_emoji/resfile/emoji/286/apng/286.png" class="qmoji" alt=":/尴尬:" title=":/尴尬:" />` -}}

{{/* 返回处理后的内容 */}}
{{- $processedContent -}}
