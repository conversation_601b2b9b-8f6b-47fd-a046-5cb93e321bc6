/**
 * QQ表情解析器
 * 基于GitHub的QQ表情映射JSON实现
 */

class QQEmojiParser {
    constructor() {
        this.emojiData = null;
        this.emojiMap = new Map();
        this.isLoading = false;
        this.loadPromise = null;
    }

    /**
     * 从GitHub获取QQ表情映射数据
     */
    async loadEmojiData() {
        if (this.isLoading) {
            return this.loadPromise;
        }

        if (this.emojiData) {
            return this.emojiData;
        }

        this.isLoading = true;
        this.loadPromise = this.fetchEmojiData();
        
        try {
            this.emojiData = await this.loadPromise;
            this.buildEmojiMap();
            return this.emojiData;
        } finally {
            this.isLoading = false;
        }
    }

    /**
     * 获取表情数据
     */
    async fetchEmojiData() {
        const url = 'https://github.com/koishijs/QFace/raw/refs/heads/master/public/assets/qq_emoji/_index.json';
        
        try {
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return await response.json();
        } catch (error) {
            console.error('Failed to load QQ emoji data:', error);
            // 返回空数组作为fallback
            return [];
        }
    }

    /**
     * 构建表情名称到数据的映射
     */
    buildEmojiMap() {
        if (!this.emojiData) return;

        this.emojiMap.clear();
        this.emojiData.forEach(emoji => {
            if (emoji.describe && emoji.describe.startsWith('/')) {
                const name = emoji.describe.substring(1); // 去掉开头的 '/'
                this.emojiMap.set(name, emoji);
            }
        });
    }

    /**
     * 根据表情名称获取表情数据
     */
    getEmojiByName(name) {
        return this.emojiMap.get(name);
    }

    /**
     * 渲染表情元素
     */
    renderEmoji(emojiData, emojiName) {
        if (!emojiData || !emojiData.assets || emojiData.assets.length === 0) {
            // 如果没有找到表情数据，返回原始文本
            return `<span class="qq-emoji-fallback">:/${emojiName}:</span>`;
        }

        const baseUrl = 'https://koishi.js.org/QFace/';
        
        // 判断是否为Super Qmoji (emojiType === 1)
        if (emojiData.emojiType === 1) {
            return this.renderSuperQmoji(emojiData, emojiName, baseUrl);
        } else {
            return this.renderNormalQmoji(emojiData, emojiName, baseUrl);
        }
    }

    /**
     * 渲染普通表情 (Normal Qmoji)
     */
    renderNormalQmoji(emojiData, emojiName, baseUrl) {
        // 查找APNG资源 (type 2)
        const apngAsset = emojiData.assets.find(asset => asset.type === 2);
        
        if (apngAsset) {
            const imgUrl = baseUrl + apngAsset.path;
            return `<img src="${imgUrl}" class="qmoji" alt=":/${emojiName}:" title=":/${emojiName}:" />`;
        }
        
        // 如果没有APNG，尝试使用缩略图 (type 0)
        const thumbAsset = emojiData.assets.find(asset => asset.type === 0);
        if (thumbAsset) {
            const imgUrl = baseUrl + thumbAsset.path;
            return `<img src="${imgUrl}" class="qmoji" alt=":/${emojiName}:" title=":/${emojiName}:" />`;
        }

        return `<span class="qq-emoji-fallback">:/${emojiName}:</span>`;
    }

    /**
     * 渲染Super表情 (Super Qmoji) - 使用Lottie动画
     */
    renderSuperQmoji(emojiData, emojiName, baseUrl) {
        // 查找Lottie JSON资源 (type 3)
        const lottieAsset = emojiData.assets.find(asset => asset.type === 3);
        
        if (lottieAsset) {
            const lottieUrl = baseUrl + lottieAsset.path;
            const uniqueId = `lottie-${emojiData.emojiId}-${Date.now()}`;
            
            // 创建Lottie容器
            const lottieHtml = `<div id="${uniqueId}" class="qmoji super-qmoji" data-lottie-url="${lottieUrl}" title=":/${emojiName}:"></div>`;
            
            // 异步加载Lottie动画
            setTimeout(() => this.loadLottieAnimation(uniqueId, lottieUrl), 0);
            
            return lottieHtml;
        }
        
        // 如果没有Lottie，降级为普通表情
        return this.renderNormalQmoji(emojiData, emojiName, baseUrl);
    }

    /**
     * 加载Lottie动画
     */
    async loadLottieAnimation(containerId, lottieUrl) {
        const container = document.getElementById(containerId);
        if (!container) return;

        // 检查是否已加载bodymovin/lottie库
        if (typeof bodymovin === 'undefined') {
            console.warn('bodymovin/lottie library not loaded, falling back to static image');
            return;
        }

        try {
            bodymovin.loadAnimation({
                container: container,
                renderer: 'svg',
                loop: true,
                autoplay: true,
                path: lottieUrl
            });
        } catch (error) {
            console.error('Failed to load Lottie animation:', error);
        }
    }

    /**
     * 处理页面中的所有QQ表情
     */
    async processEmojis() {
        // 加载表情数据
        await this.loadEmojiData();

        // 查找所有QQ表情容器
        const emojiContainers = document.querySelectorAll('.qq-emoji-container');
        
        emojiContainers.forEach(container => {
            const emojiName = container.getAttribute('data-emoji-name');
            if (!emojiName) return;

            const emojiData = this.getEmojiByName(emojiName);
            const renderedHtml = this.renderEmoji(emojiData, emojiName);
            
            container.innerHTML = renderedHtml;
        });
    }
}

// 创建全局实例
window.qqEmojiParser = new QQEmojiParser();

// 页面加载完成后处理表情
document.addEventListener('DOMContentLoaded', () => {
    window.qqEmojiParser.processEmojis();
});

// 如果页面已经加载完成，立即处理
if (document.readyState === 'loading') {
    // 文档还在加载中，等待DOMContentLoaded事件
} else {
    // 文档已经加载完成，立即处理
    window.qqEmojiParser.processEmojis();
}
